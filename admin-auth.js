/**
 * Per Ankh Admin Authentication Library
 * Provides content protection and authentication utilities
 */

class PerAnkhAuth {
    constructor() {
        this.adminSessionKey = 'perankh_admin_session';
        this.adminActiveKey = 'perankh_admin_active';
        this.memberSessionKey = 'perankh_member_session';
        this.memberActiveKey = 'perankh_member_active';
        this.onboardingKey = 'perankh_admin_onboarding_complete';
        this.protectedElements = [];
        this.onboardingStep = 1;
        this.onboardingMaxSteps = 4;
        this.init();
    }

    init() {
        // Initialize authentication system
        this.checkAuthStatus();
        this.protectContent();
        this.addAuthUI();
        this.createOnboardingModal();
        
        // Auto-check authentication every 5 minutes
        setInterval(() => this.checkAuthStatus(), 5 * 60 * 1000);
    }

    async login(username, password) {
        const validCredentials = {
            'BabaBaqui': 'PerAnkh13',
            'MamaAyana': 'PerAnkh13'
        };

        if (validCredentials[username] === password) {
            this.setAdminSession(username);
            
            if (!this.hasCompletedOnboarding()) {
                this.showOnboarding();
            } else {
                window.location.href = 'admin-dashboard.html';
            }
            return { success: true };
        } else {
            return { success: false, message: 'Invalid credentials. Access denied.' };
        }
    }

    logout() {
        localStorage.removeItem(this.adminSessionKey);
        sessionStorage.removeItem(this.adminActiveKey);
        localStorage.removeItem(this.memberSessionKey);
        sessionStorage.removeItem(this.memberActiveKey);
        window.location.href = 'admin-login.html';
    }

    setAdminSession(username) {
        const sessionData = {
            username: username,
            loginTime: new Date().toISOString(),
            sessionId: 'sess_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36),
            role: this.getRoleForUser(username)
        };

        localStorage.setItem(this.adminSessionKey, JSON.stringify(sessionData));
        sessionStorage.setItem(this.adminActiveKey, 'true');
    }

    getRoleForUser(username) {
        if (username === 'BabaBaqui' || username === 'MamaAyana') {
            return 'super_admin';
        }
        return 'admin';
    }

    hasCompletedOnboarding() {
        return localStorage.getItem(this.onboardingKey) === 'true';
    }

    createOnboardingModal() {
        if (document.getElementById('onboardingModal')) return;

        const modalHTML = `
            <div id="onboardingModal" class="onboarding-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.9); align-items: center; justify-content: center; z-index: 1000;">
                <div class="onboarding-content" style="background: linear-gradient(135deg, #663399 0%, #8B4513 100%); border: 2px solid #DAA520; border-radius: 20px; padding: 3rem; max-width: 600px; width: 90%; text-align: center; position: relative; color: #F5F5DC;">
                    <div class="step-indicator" style="display: flex; justify-content: center; margin-bottom: 2rem;">
                        <div class="step-dot active" data-step="1" style="width: 12px; height: 12px; border-radius: 50%; background: rgba(218, 165, 32, 0.3); margin: 0 5px; transition: all 0.3s ease;"></div>
                        <div class="step-dot" data-step="2" style="width: 12px; height: 12px; border-radius: 50%; background: rgba(218, 165, 32, 0.3); margin: 0 5px; transition: all 0.3s ease;"></div>
                        <div class="step-dot" data-step="3" style="width: 12px; height: 12px; border-radius: 50%; background: rgba(218, 165, 32, 0.3); margin: 0 5px; transition: all 0.3s ease;"></div>
                        <div class="step-dot" data-step="4" style="width: 12px; height: 12px; border-radius: 50%; background: rgba(218, 165, 32, 0.3); margin: 0 5px; transition: all 0.3s ease;"></div>
                    </div>
                    <div class="onboarding-step active" data-step="1">
                        <div class="ankh-symbol" style="font-family: 'Cinzel', serif; font-size: 3rem; color: #DAA520; text-align: center; margin-bottom: 1rem;">☥</div>
                        <h2 class="text-2xl font-bold mb-4" style="color: #DAA520;">Welcome to Per Ankh Admin</h2>
                        <p class="mb-6">You have successfully authenticated. Let's begin your orientation.</p>
                        <button class="onboarding-button" onclick="perAnkhAuth.nextOnboardingStep()">Begin Journey</button>
                    </div>
                    <div class="onboarding-step" data-step="2" style="display: none;">
                        <i class="fas fa-chart-line text-4xl mb-4" style="color: #DAA520;"></i>
                        <h2 class="text-2xl font-bold mb-4" style="color: #DAA520;">Admin Dashboard</h2>
                        <p class="mb-6">Your dashboard provides real-time insights into community growth and spiritual metrics.</p>
                        <div class="flex justify-center space-x-4"><button class="onboarding-button" onclick="perAnkhAuth.prevOnboardingStep()">Previous</button><button class="onboarding-button" onclick="perAnkhAuth.nextOnboardingStep()">Continue</button></div>
                    </div>
                    <div class="onboarding-step" data-step="3" style="display: none;">
                        <i class="fas fa-users-cog text-4xl mb-4" style="color: #DAA520;"></i>
                        <h2 class="text-2xl font-bold mb-4" style="color: #DAA520;">Admin Capabilities</h2>
                        <div class="text-left mb-6" style="display: inline-block;"><ul class="space-y-2"><li><i class="fas fa-check text-green-400 mr-2"></i>Manage member profiles</li><li><i class="fas fa-check text-green-400 mr-2"></i>Create and edit content</li><li><i class="fas fa-check text-green-400 mr-2"></i>Monitor safety protocols</li></ul></div>
                        <div class="flex justify-center space-x-4"><button class="onboarding-button" onclick="perAnkhAuth.prevOnboardingStep()">Previous</button><button class="onboarding-button" onclick="perAnkhAuth.nextOnboardingStep()">Continue</button></div>
                    </div>
                    <div class="onboarding-step" data-step="4" style="display: none;">
                        <i class="fas fa-star text-4xl mb-4" style="color: #DAA520;"></i>
                        <h2 class="text-2xl font-bold mb-4" style="color: #DAA520;">Ready to Begin</h2>
                        <p class="mb-6">You are now prepared to guide the Per Ankh community. May your administration bring wisdom to all.</p>
                        <button class="onboarding-button" onclick="perAnkhAuth.completeOnboarding()">Enter Dashboard</button>
                    </div>
                </div>
            </div>`;
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const style = document.createElement('style');
        style.textContent = `
            .onboarding-button { background: #DAA520; color: #8B4513; border: none; padding: 0.75rem 2rem; border-radius: 8px; font-weight: 600; cursor: pointer; margin: 0 0.5rem; transition: all 0.3s ease; }
            .onboarding-button:hover { background: #FFBF00; transform: translateY(-2px); }
            .step-dot.active { background: #DAA520 !important; transform: scale(1.2); }
        `;
        document.head.appendChild(style);
    }

    showOnboarding() {
        const modal = document.getElementById('onboardingModal');
        if (modal) modal.style.display = 'flex';
    }
    
    hideOnboarding() {
        const modal = document.getElementById('onboardingModal');
        if (modal) modal.style.display = 'none';
    }

    nextOnboardingStep() {
        if (this.onboardingStep < this.onboardingMaxSteps) this.updateOnboardingStep(this.onboardingStep + 1);
    }

    prevOnboardingStep() {
        if (this.onboardingStep > 1) this.updateOnboardingStep(this.onboardingStep - 1);
    }
    
    updateOnboardingStep(newStep) {
        document.querySelector(`#onboardingModal .onboarding-step[data-step="${this.onboardingStep}"]`).style.display = 'none';
        document.querySelector(`#onboardingModal .onboarding-step[data-step="${this.onboardingStep}"]`).classList.remove('active');
        document.querySelector(`#onboardingModal .step-dot[data-step="${this.onboardingStep}"]`).classList.remove('active');
        
        this.onboardingStep = newStep;

        document.querySelector(`#onboardingModal .onboarding-step[data-step="${this.onboardingStep}"]`).style.display = 'block';
        document.querySelector(`#onboardingModal .onboarding-step[data-step="${this.onboardingStep}"]`).classList.add('active');
        document.querySelector(`#onboardingModal .step-dot[data-step="${this.onboardingStep}"]`).classList.add('active');
    }

    completeOnboarding() {
        localStorage.setItem(this.onboardingKey, 'true');
        this.hideOnboarding();
        window.location.href = 'admin-dashboard.html';
    }

    /**
     * Check if user is authenticated (admin or member)
     */
    isAuthenticated() {
        return this.isAdminAuthenticated() || this.isMemberAuthenticated();
    }

    /**
     * Check if user is authenticated as admin
     */
    isAdminAuthenticated() {
        const session = localStorage.getItem(this.adminSessionKey);
        const active = sessionStorage.getItem(this.adminActiveKey);
        
        if (!session || !active) return false;

        try {
            const sessionData = JSON.parse(session);
            const loginTime = new Date(sessionData.loginTime);
            const now = new Date();
            const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

            // Session expires after 8 hours
            if (hoursDiff >= 8) {
                this.adminLogout();
                return false;
            }

            return true;
        } catch (error) {
            console.error('Admin auth check error:', error);
            return false;
        }
    }

    /**
     * Check if user is authenticated as member
     */
    isMemberAuthenticated() {
        const session = localStorage.getItem(this.memberSessionKey);
        const active = sessionStorage.getItem(this.memberActiveKey);
        
        if (!session || !active) return false;

        try {
            const sessionData = JSON.parse(session);
            const loginTime = new Date(sessionData.loginTime);
            const now = new Date();
            const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

            // Session expires after 8 hours
            if (hoursDiff >= 8) {
                this.memberLogout();
                return false;
            }

            return true;
        } catch (error) {
            console.error('Member auth check error:', error);
            return false;
        }
    }

    /**
     * Get user type (guest, member, admin)
     */
    getUserType() {
        if (this.isAdminAuthenticated()) return 'admin';
        if (this.isMemberAuthenticated()) return 'member';
        return 'guest';
    }

    /**
     * Get current user session data
     */
    getSession() {
        try {
            // Check admin session first
            let session = localStorage.getItem(this.adminSessionKey);
            if (session) {
                return JSON.parse(session);
            }
            
            // Check member session
            session = localStorage.getItem(this.memberSessionKey);
            return session ? JSON.parse(session) : null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Check authentication status and update UI
     */
    checkAuthStatus() {
        const isAuth = this.isAuthenticated();
        const userType = this.getUserType();
        const authElements = document.querySelectorAll('[data-auth-required]');
        const adminElements = document.querySelectorAll('.admin-only');

        // Show/hide protected content
        authElements.forEach(element => {
            if (isAuth) {
                element.style.display = element.dataset.originalDisplay || 'block';
                element.classList.remove('auth-hidden');
            } else {
                element.dataset.originalDisplay = element.style.display;
                element.style.display = 'none';
                element.classList.add('auth-hidden');
            }
        });

        adminElements.forEach(element => {
            element.style.display = isAuth ? 'block' : 'none';
        });

        // Update navigation based on user type
        this.updateNavigationForUserType(userType);
    }

    /**
     * Update navigation based on user type
     */
    updateNavigationForUserType(userType) {
        // This will be implemented in website.html
        if (typeof window.updateNavigationForUserType === 'function') {
            window.updateNavigationForUserType(userType);
        }
    }

    /**
     * Format user role for display
     */
    formatRole(role) {
        const roleMap = {
            'super_admin': 'Super Admin',
            'admin': 'Admin',
            'moderator': 'Moderator',
            'content_manager': 'Content Manager'
        };
        return roleMap[role] || 'Admin';
    }

    /**
     * Protect content elements
     */
    protectContent() {
        // Find all elements with protection attributes
        const protectedElements = document.querySelectorAll('[data-admin-only], [data-auth-required]');
        
        protectedElements.forEach(element => {
            if (!this.isAuthenticated()) {
                // Create protection overlay
                const overlay = this.createProtectionOverlay(element);
                element.style.position = 'relative';
                element.appendChild(overlay);
                
                // Store reference
                this.protectedElements.push({
                    element: element,
                    overlay: overlay
                });
            }
        });
    }

    /**
     * Create protection overlay for restricted content
     */
    createProtectionOverlay(element) {
        const overlay = document.createElement('div');
        overlay.className = 'admin-protection-overlay';
        overlay.innerHTML = `
            <div class="protection-content">
                <div class="protection-icon">🔒</div>
                <div class="protection-title">Admin Access Required</div>
                <div class="protection-message">This content is restricted to authenticated administrators.</div>
                <button class="protection-login-btn" onclick="perAnkhAuth.redirectToLogin()">
                    Admin Login
                </button>
            </div>
        `;
        
        // Add styles
        overlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-radius: inherit;
        `;

        return overlay;
    }

    /**
     * Add authentication UI elements to navigation
     */
    addAuthUI() {
        const nav = document.querySelector('.nav-links');
        if (!nav) return;

        // Check if auth UI already exists
        if (document.getElementById('adminLoginBtn')) return;

        // Create admin UI elements
        const adminUI = document.createElement('div');
        adminUI.className = 'admin-ui';
        adminUI.innerHTML = `
            <div id="adminInfo" class="admin-info" style="display: none;">
                <span class="admin-welcome"></span>
                <span class="admin-role"></span>
            </div>
            <button id="adminLoginBtn" class="nav-link admin-login-btn" onclick="perAnkhAuth.redirectToLogin()">
                <i class="fas fa-user-shield mr-2"></i>Admin Login
            </button>
            <div id="adminLogoutBtn" class="admin-logout-section" style="display: none;">
                <a href="admin-dashboard.html" class="nav-link">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
                <button class="nav-link admin-logout-btn" onclick="perAnkhAuth.logout()">
                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                </button>
            </div>
        `;

        // Add styles for admin UI
        const style = document.createElement('style');
        style.textContent = `
            .admin-ui {
                display: flex;
                align-items: center;
                gap: 1rem;
            }
            
            .admin-info {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                padding: 0.5rem 1rem;
                background: rgba(45, 27, 105, 0.3);
                border-radius: 20px;
                border: 1px solid rgba(212, 175, 55, 0.2);
            }
            
            .admin-welcome {
                color: var(--primary-gold);
                font-weight: 600;
                font-size: 0.9rem;
            }
            
            .admin-role {
                color: rgba(248, 248, 255, 0.7);
                font-size: 0.8rem;
            }
            
            .admin-login-btn, .admin-logout-btn {
                background: linear-gradient(135deg, var(--primary-gold) 0%, var(--deep-gold) 100%);
                color: var(--matte-black) !important;
                border: none;
                padding: 0.6rem 1.2rem;
                border-radius: 20px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
            }
            
            .admin-login-btn:hover, .admin-logout-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
                color: var(--matte-black) !important;
            }
            
            .admin-logout-section {
                display: flex;
                gap: 1rem;
                align-items: center;
            }
            
            .admin-protection-overlay {
                border: 1px solid rgba(212, 175, 55, 0.3);
            }
            
            .protection-content {
                text-align: center;
                padding: 2rem;
                max-width: 300px;
            }
            
            .protection-icon {
                font-size: 3rem;
                margin-bottom: 1rem;
                opacity: 0.7;
            }
            
            .protection-title {
                font-family: 'Cinzel', serif;
                font-size: 1.2rem;
                font-weight: 600;
                color: var(--primary-gold);
                margin-bottom: 0.5rem;
            }
            
            .protection-message {
                color: rgba(248, 248, 255, 0.8);
                margin-bottom: 1.5rem;
                line-height: 1.4;
            }
            
            .protection-login-btn {
                background: linear-gradient(135deg, var(--primary-gold) 0%, var(--deep-gold) 100%);
                color: var(--matte-black);
                border: none;
                padding: 0.8rem 1.5rem;
                border-radius: 20px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            
            .protection-login-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
            }
            
            @media (max-width: 1024px) {
                .admin-ui {
                    flex-direction: column;
                    gap: 0.5rem;
                    width: 100%;
                }
                
                .admin-logout-section {
                    flex-direction: column;
                    gap: 0.5rem;
                    width: 100%;
                }
                
                .admin-info {
                    align-items: center;
                    width: 100%;
                }
            }
        `;
        
        document.head.appendChild(style);
        nav.appendChild(adminUI);
    }

    /**
     * Redirect to admin login page
     */
    redirectToLogin() {
        window.location.href = 'admin-login.html';
    }

    /**
     * Show admin content (used in admin pages)
     */
    showAdminContent() {
        // Update admin info in navigation if present
        const adminInfo = document.getElementById('adminInfo');
        const loginBtn = document.getElementById('adminLoginBtn');
        const logoutBtn = document.getElementById('adminLogoutBtn');
        
        if (adminInfo) {
            const sessionData = this.getSession();
            if (sessionData) {
                const welcomeSpan = adminInfo.querySelector('.admin-welcome');
                const roleSpan = adminInfo.querySelector('.admin-role');
                
                if (welcomeSpan) {
                    welcomeSpan.textContent = `Welcome, ${sessionData.username}`;
                }
                
                if (roleSpan) {
                    roleSpan.textContent = `(${this.formatRole(sessionData.role)})`;
                }
                
                adminInfo.style.display = 'flex';
                if (loginBtn) loginBtn.style.display = 'none';
                if (logoutBtn) logoutBtn.style.display = 'inline-block';
            }
        }
    }

    /**
     * Show admin-only content
     */
    showAdminOnlyContent() {
        if (!this.isAdminAuthenticated()) {
            alert('Admin access required. Please login first.');
            this.redirectToLogin();
            return false;
        }
        return true;
    }

    /**
     * Protect a function call with authentication
     */
    requireAuth(callback) {
        if (this.isAuthenticated()) {
            return callback();
        } else {
            alert('Admin access required. Please login first.');
            this.redirectToLogin();
            return false;
        }
    }

    /**
     * Add protection to specific elements
     */
    protectElement(selector, options = {}) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.setAttribute('data-auth-required', 'true');
            if (options.adminOnly) {
                element.setAttribute('data-admin-only', 'true');
            }
        });
        
        // Re-run protection check
        this.checkAuthStatus();
    }

    /**
     * Remove protection from elements
     */
    unprotectElement(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.removeAttribute('data-auth-required');
            element.removeAttribute('data-admin-only');
            
            // Remove any existing overlays
            const overlay = element.querySelector('.admin-protection-overlay');
            if (overlay) {
                overlay.remove();
            }
        });
        
        this.checkAuthStatus();
    }
}

// Initialize global authentication instance
const perAnkhAuth = new PerAnkhAuth();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerAnkhAuth;
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        perAnkhAuth.checkAuthStatus();
    });
} else {
    perAnkhAuth.checkAuthStatus();
}