<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ad<PERSON> - Per <PERSON> Entheogenic Church</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --earth-brown: #8B4513;
            --forest-green: #228B22;
            --golden-ankh: #DAA520;
            --sacred-amber: #FFBF00;
            --mycelium-white: #F5F5DC;
            --consciousness-purple: #663399;
        }
        
        .earth-gradient {
            background: linear-gradient(135deg, var(--forest-green) 0%, var(--earth-brown) 50%, var(--golden-ankh) 100%);
        }
        
        .consciousness-gradient {
            background: linear-gradient(135deg, var(--consciousness-purple) 0%, var(--forest-green) 100%);
        }
        
        .sacred-pattern {
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(218, 165, 32, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(34, 139, 34, 0.1) 0%, transparent 50%);
        }
        
        body {
            background: linear-gradient(135deg, var(--forest-green) 0%, var(--earth-brown) 50%, var(--golden-ankh) 100%);
            font-family: 'Inter', sans-serif;
            color: var(--mycelium-white);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            padding-top: 80px; /* Account for fixed navbar */
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(218, 165, 32, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(34, 139, 34, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
        }
        
        .sacred-geometry {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            opacity: 0.2;
        }
        
        .geometry-element {
            position: absolute;
            border: 1px solid var(--golden-ankh);
            opacity: 0.15;
            animation: sacredFloat 12s ease-in-out infinite;
        }
        
        .geometry-1 {
            width: 300px;
            height: 300px;
            top: 10%;
            left: 5%;
            border-radius: 50%;
            animation-delay: 0s;
        }
        
        .geometry-2 {
            width: 200px;
            height: 200px;
            top: 50%;
            right: 10%;
            border-radius: 50%;
            animation-delay: 4s;
        }
        
        .geometry-3 {
            width: 150px;
            height: 150px;
            top: 30%;
            left: 50%;
            transform: rotate(45deg);
            animation-delay: 8s;
        }
        
        @keyframes sacredFloat {
            0%, 100% { 
                transform: translateY(0px) rotate(0deg);
                opacity: 0.1;
            }
            25% {
                transform: translateY(-15px) rotate(90deg);
                opacity: 0.2;
            }
            50% { 
                transform: translateY(-30px) rotate(180deg);
                opacity: 0.15;
            }
            75% {
                transform: translateY(-15px) rotate(270deg);
                opacity: 0.2;
            }
        }
        
        .login-container {
            background: rgba(0, 0, 0, 0.85);
            border: 2px solid var(--golden-ankh);
            border-radius: 20px;
            padding: 3rem;
            backdrop-filter: blur(20px);
            box-shadow: 
                0 20px 60px rgba(218, 165, 32, 0.2),
                0 0 40px rgba(34, 139, 34, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            width: 100%;
            max-width: 450px;
            position: relative;
            z-index: 10;
            animation: containerGlow 6s ease-in-out infinite alternate;
        }
        
        @keyframes containerGlow {
            0% {
                box-shadow: 
                    0 20px 60px rgba(218, 165, 32, 0.2),
                    0 0 40px rgba(34, 139, 34, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1);
                border-color: var(--golden-ankh);
            }
            100% {
                box-shadow: 
                    0 25px 80px rgba(218, 165, 32, 0.3),
                    0 0 60px rgba(34, 139, 34, 0.15),
                    inset 0 1px 0 rgba(255, 255, 255, 0.15);
                border-color: var(--sacred-amber);
            }
        }
        
        .ankh-symbol {
            font-family: 'Cinzel', serif;
            font-size: 3rem;
            color: var(--golden-ankh);
            text-align: center;
            margin-bottom: 1rem;
            animation: ankhPulse 3s ease-in-out infinite alternate;
            text-shadow: 0 0 20px rgba(218, 165, 32, 0.6);
        }
        
        @keyframes ankhPulse {
            0% {
                transform: scale(1);
                text-shadow: 0 0 20px rgba(218, 165, 32, 0.6);
            }
            100% {
                transform: scale(1.1);
                text-shadow: 0 0 30px rgba(218, 165, 32, 0.8);
            }
        }
        
        .login-title {
            font-family: 'Cinzel', serif;
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
            color: var(--golden-ankh);
            margin-bottom: 0.5rem;
        }
        
        .login-subtitle {
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            color: var(--golden-ankh);
            font-weight: 500;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .form-input {
            width: 100%;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.6);
            border: 1px solid rgba(218, 165, 32, 0.3);
            border-radius: 10px;
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--golden-ankh);
            box-shadow: 0 0 15px rgba(218, 165, 32, 0.3);
            background: rgba(0, 0, 0, 0.8);
        }
        
        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }
        
        .login-button {
            width: 100%;
            background: linear-gradient(135deg, var(--golden-ankh) 0%, var(--sacred-amber) 100%);
            color: var(--earth-brown);
            padding: 1rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }
        
        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(218, 165, 32, 0.4);
        }
        
        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .error-message {
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
            color: #fca5a5;
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            display: none;
        }
        
        .success-message {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #86efac;
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            display: none;
        }
        
        .back-link {
            text-align: center;
            margin-top: 2rem;
        }
        
        .back-link a {
            color: var(--golden-ankh);
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .back-link a:hover {
            color: white;
            text-shadow: 0 0 8px rgba(218, 165, 32, 0.4);
        }
        
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(139, 69, 19, 0.3);
            border-top: 2px solid var(--earth-brown);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Onboarding Modal Styles */
        .onboarding-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .onboarding-content {
            background: linear-gradient(135deg, var(--consciousness-purple) 0%, var(--earth-brown) 100%);
            border: 2px solid var(--golden-ankh);
            border-radius: 20px;
            padding: 3rem;
            max-width: 600px;
            width: 90%;
            text-align: center;
            position: relative;
        }
        
        .onboarding-step {
            display: none;
        }
        
        .onboarding-step.active {
            display: block;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(218, 165, 32, 0.3);
            margin: 0 5px;
            transition: all 0.3s ease;
        }
        
        .step-dot.active {
            background: var(--golden-ankh);
            transform: scale(1.2);
        }
        
        .onboarding-button {
            background: var(--golden-ankh);
            color: var(--earth-brown);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        
        .onboarding-button:hover {
            background: var(--sacred-amber);
            transform: translateY(-2px);
        }
        
        @media (max-width: 480px) {
            .login-container, .onboarding-content {
                margin: 1rem;
                padding: 2rem;
            }
            
            .login-title {
                font-size: 1.5rem;
            }
            
            .ankh-symbol {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="bg-gray-800 shadow-lg fixed w-full top-0 z-50">
        <div class="container mx-auto px-6">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <span class="ankh-symbol text-3xl mr-3">☥</span>
                    <a href="index.html" class="text-2xl font-bold" style="color: var(--golden-ankh); font-family: 'Cinzel', serif;">PER ANKH ENTHEOGENIC CHURCH</a>
                </div>
                <div class="hidden md:flex space-x-6">
                    <a href="become-a-member.html" class="text-white hover:text-yellow-400 transition-colors">Become a Member</a>
                    <a href="events.html" class="text-white hover:text-yellow-400 transition-colors">Events</a>
                    <a href="per-ankh.html" class="text-white hover:text-yellow-400 transition-colors">Ethos & Structure</a>
                    <a href="ceremony-and-safety.html" class="text-white hover:text-yellow-400 transition-colors">Ceremony and Safety</a>
                    <a href="donate.html" class="text-white hover:text-yellow-400 transition-colors">Donate</a>
                    <a href="contact.html" class="text-white hover:text-yellow-400 transition-colors">Contact</a>
                    <a href="member-login.html" class="bg-yellow-500 text-gray-900 px-4 py-2 rounded-lg font-semibold hover:bg-yellow-400 transition-colors">Member Login</a>
                </div>
                
                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button id="mobileMenuBtn" class="text-white hover:text-yellow-400 transition-colors">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- Mobile Menu -->
            <div id="mobileMenu" class="md:hidden mt-4 pb-4 border-t border-gray-600 hidden">
                <div class="flex flex-col space-y-4 mt-4">
                    <a href="become-a-member.html" class="text-white hover:text-yellow-400 transition-colors">Become a Member</a>
                    <a href="events.html" class="text-white hover:text-yellow-400 transition-colors">Events</a>
                    <a href="per-ankh.html" class="text-white hover:text-yellow-400 transition-colors">Ethos & Structure</a>
                    <a href="ceremony-and-safety.html" class="text-white hover:text-yellow-400 transition-colors">Ceremony and Safety</a>
                    <a href="donate.html" class="text-white hover:text-yellow-400 transition-colors">Donate</a>
                    <a href="contact.html" class="text-white hover:text-yellow-400 transition-colors">Contact</a>
                    <a href="member-login.html" class="bg-yellow-500 text-gray-900 px-4 py-2 rounded-lg font-semibold hover:bg-yellow-400 transition-colors inline-block">Member Login</a>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Sacred Geometry Background -->
    <div class="sacred-geometry">
        <div class="geometry-element geometry-1"></div>
        <div class="geometry-element geometry-2"></div>
        <div class="geometry-element geometry-3"></div>
    </div>

    <!-- Login Container -->
    <div class="login-container">
        <div class="ankh-symbol">☥</div>
        <h1 class="login-title">Admin Portal</h1>
        <p class="login-subtitle">Sacred Access to Per Ankh Administration</p>

        <div id="errorMessage" class="error-message"></div>
        <div id="successMessage" class="success-message"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username" class="form-label">
                    <i class="fas fa-user mr-2"></i>Username
                </label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    class="form-input" 
                    placeholder="Enter your username"
                    required
                    autocomplete="username"
                >
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock mr-2"></i>Password
                </label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    class="form-input" 
                    placeholder="Enter your password"
                    required
                    autocomplete="current-password"
                >
            </div>

            <button type="submit" id="loginButton" class="login-button">
                <div class="loading-spinner" id="loadingSpinner"></div>
                <span id="buttonText">Access Sacred Portal</span>
            </button>
        </form>

        <div class="back-link">
            <a href="index.html">
                <i class="fas fa-arrow-left mr-2"></i>
                Return to Main Site
            </a>
        </div>
    </div>

        <script src="admin-auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const loginForm = document.getElementById('loginForm');
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');
            const loginButton = document.getElementById('loginButton');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const buttonText = document.getElementById('buttonText');

            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;

                errorDiv.style.display = 'none';
                successDiv.style.display = 'none';

                if (!username || !password) {
                    showError('Please enter both username and password.');
                    return;
                }

                loginButton.disabled = true;
                loadingSpinner.style.display = 'inline-block';
                buttonText.textContent = 'Authenticating...';

                try {
                    const result = await perAnkhAuth.login(username, password);

                    if (result.success) {
                        showSuccess('Authentication successful! Preparing your sacred workspace...');
                    } else {
                        showError(result.message);
                        resetButton();
                    }
                } catch (error) {
                    console.error("Login error:", error);
                    showError('An unexpected error occurred. Please try again.');
                    resetButton();
                }
            });

            function showError(message) {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            }

            function showSuccess(message) {
                successDiv.textContent = message;
                successDiv.style.display = 'block';
            }

            function resetButton() {
                loginButton.disabled = false;
                loadingSpinner.style.display = 'none';
                buttonText.textContent = 'Access Sacred Portal';
            }
        });
    </script>
</body>
</html>
